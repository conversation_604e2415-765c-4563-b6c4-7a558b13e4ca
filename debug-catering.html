<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用餐组件调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .debug-title {
            font-size: 18px;
            font-weight: bold;
            color: #1868db;
            margin-bottom: 15px;
        }
        .debug-item {
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #1868db;
        }
        .debug-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .debug-step {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 12px;
            border-radius: 4px;
            margin: 8px 0;
        }
        .warning {
            background: #fff7e6;
            border: 1px solid #ffd591;
            color: #d46b08;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="debug-title">🔍 用餐组件单价回显问题调试指南</div>
        
        <div class="debug-item">
            <strong>问题描述：</strong>
            本地环境有回显，测试环境没有回显，单价在满足条件时应该禁止输入并自动赋值
        </div>

        <div class="debug-item">
            <strong>关键逻辑：</strong>
            当 <code>isCateringStandardControl === '1'</code> 时，单价输入框禁用并自动赋值 <code>schemeUnitPrice || demandUnitPrice</code>
        </div>
    </div>

    <div class="debug-container">
        <div class="debug-title">🛠️ 调试步骤</div>
        
        <div class="debug-step">
            <strong>步骤1：检查控制台日志</strong>
            <p>打开浏览器开发者工具，查看控制台是否有以下日志：</p>
            <div class="debug-code">
[餐标控制变化监听] isCateringStandardControl: 1, 数据条数: X
[餐标控制=1，自动赋值单价] 索引0: schemeUnitPrice=XX, demandUnitPrice=XX
[单价赋值成功] 索引0: 新的billUnitPrice=XX
            </div>
        </div>

        <div class="debug-step">
            <strong>步骤2：手动调试</strong>
            <p>在浏览器控制台执行以下代码来手动触发调试：</p>
            <div class="debug-code">
// 获取父组件实例（需要根据实际情况调整选择器）
const planComponent = document.querySelector('.interact_schedule_plan').__vueParentComponent;

// 调用调试方法
if (planComponent && planComponent.exposed && planComponent.exposed.debugCateringComponents) {
    planComponent.exposed.debugCateringComponents();
}
            </div>
        </div>

        <div class="debug-step">
            <strong>步骤3：检查数据源</strong>
            <p>确认测试环境的数据是否完整：</p>
            <div class="debug-code">
// 检查原始数据
console.log('demandInfo.caterings:', demandInfo.caterings);

// 检查每个用餐项的关键字段
demandInfo.caterings.forEach((item, index) => {
    console.log(`用餐${index + 1}:`, {
        schemeUnitPrice: item.schemeUnitPrice,
        demandUnitPrice: item.demandUnitPrice,
        id: item.id
    });
});
            </div>
        </div>

        <div class="debug-step warning">
            <strong>步骤4：检查 isCateringStandardControl 值</strong>
            <p>确认控制参数是否正确传递：</p>
            <div class="debug-code">
// 检查控制参数
console.log('isCateringStandardControl:', isCateringStandardControl);

// 应该是字符串 '1'，不是数字 1
if (isCateringStandardControl !== '1') {
    console.warn('控制参数不是字符串1，当前值:', typeof isCateringStandardControl, isCateringStandardControl);
}
            </div>
        </div>
    </div>

    <div class="debug-container">
        <div class="debug-title">🚨 常见问题及解决方案</div>
        
        <div class="debug-step error">
            <strong>问题1：schemeUnitPrice 和 demandUnitPrice 都为空</strong>
            <p>解决方案：检查后端接口返回的数据，确保这两个字段有值</p>
        </div>

        <div class="debug-step error">
            <strong>问题2：isCateringStandardControl 不是字符串 '1'</strong>
            <p>解决方案：检查父组件传递的 props，确保类型正确</p>
        </div>

        <div class="debug-step error">
            <strong>问题3：数据初始化时机问题</strong>
            <p>解决方案：确保 isCateringStandardControl 在数据加载完成后设置</p>
        </div>

        <div class="debug-step success">
            <strong>临时解决方案：手动触发重新计算</strong>
            <p>如果自动逻辑失效，可以手动调用：</p>
            <div class="debug-code">
// 手动触发重新计算（在组件实例上调用）
cateringRef.value.forEach(ref => {
    if (ref.forceRecalculatePrice) {
        ref.forceRecalculatePrice();
    }
});
            </div>
        </div>
    </div>

    <div class="debug-container">
        <div class="debug-title">📝 修改说明</div>
        
        <div class="debug-item">
            <strong>已添加的调试功能：</strong>
            <ul>
                <li>增强了控制台日志输出，显示详细的赋值过程</li>
                <li>添加了 <code>forceRecalculatePrice()</code> 方法强制重新计算</li>
                <li>在父组件添加了 <code>debugCateringComponents()</code> 调试方法</li>
                <li>改进了数据初始化时的单价设置逻辑</li>
            </ul>
        </div>

        <div class="debug-item">
            <strong>建议的测试流程：</strong>
            <ol>
                <li>在测试环境打开页面</li>
                <li>打开浏览器开发者工具</li>
                <li>查看控制台日志，确认数据和控制参数</li>
                <li>如果自动逻辑失效，手动调用调试方法</li>
                <li>对比本地和测试环境的数据差异</li>
            </ol>
        </div>
    </div>
</body>
</html>
